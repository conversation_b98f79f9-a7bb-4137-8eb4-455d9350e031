import React, { useState, useRef } from 'react';
import { Play, Pause, Download, Loader2, Send, Trash2, AlertCircle, CheckCircle } from 'lucide-react';
import VoiceCloningAPI from '../services/api';

interface TTSRequest {
  text: string;
  quality_mode: 'high' | 'standard';
  remove_silence: boolean;
}

interface TTSSectionProps {
  isSystemReady: boolean;
  hasVoice: boolean;
}

const TTSSection: React.FC<TTSSectionProps> = ({ isSystemReady, hasVoice }) => {
  const [text, setText] = useState('');
  const [qualityMode, setQualityMode] = useState<'high' | 'standard'>('high');
  const [removeSilence, setRemoveSilence] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [status, setStatus] = useState('');
  const [generatedAudioId, setGeneratedAudioId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  const handleGenerateTTS = async () => {
    if (!text.trim()) {
      setStatus('Please enter some text to generate speech');
      return;
    }

    if (!hasVoice) {
      setStatus('Please clone a voice first');
      return;
    }

    setIsGenerating(true);
    setStatus('Generating speech...');

    try {
      const request: TTSRequest = {
        text: text.trim(),
        quality_mode: qualityMode,
        remove_silence: removeSilence,
      };

      const response = await VoiceCloningAPI.generateTTS(request);
      
      if (response.success && response.audio_id) {
        setStatus(response.message);
        setGeneratedAudioId(response.audio_id);
      } else {
        setStatus(response.message || 'Failed to generate speech');
      }
    } catch (error) {
      console.error('TTS generation error:', error);

      // Check if it's a connection error
      const isConnected = await VoiceCloningAPI.checkConnection();
      if (!isConnected) {
        setStatus('Error: Failed to connect to backend. Please check if the backend server is running on port 8000.');
      } else {
        setStatus('Error: Failed to generate speech. Please try again or check the console for details.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const togglePlayback = () => {
    if (!audioRef.current || !generatedAudioId) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const downloadAudio = () => {
    if (generatedAudioId) {
      const url = VoiceCloningAPI.getAudioUrl(generatedAudioId);
      const a = document.createElement('a');
      a.href = url;
      a.download = `clonie_generated_${generatedAudioId}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const clearText = () => {
    setText('');
    setStatus('');
  };

  const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;

  return (
    <div className="space-y-6">
      {/* Text Input */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Enter Text to Generate
        </label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Type or paste your text here..."
          rows={8}
          className="input-field resize-none"
          disabled={!hasVoice}
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-xs text-gray-500">
            {wordCount} words • Keep under 500 words for best quality
          </span>
          {wordCount > 500 && (
            <span className="text-xs text-amber-600 flex items-center">
              <AlertCircle className="w-3 h-3 mr-1" />
              Consider shorter text for optimal quality
            </span>
          )}
        </div>
      </div>

      {/* Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Quality Mode */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Quality Mode
          </label>
          <div className="space-y-2">
            {[
              { value: 'high', label: 'High Quality', desc: 'Best quality, slower generation' },
              { value: 'standard', label: 'Standard', desc: 'Good quality, faster generation' },
            ].map((option) => (
              <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="quality"
                  value={option.value}
                  checked={qualityMode === option.value}
                  onChange={(e) => setQualityMode(e.target.value as any)}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                  disabled={!hasVoice}
                />
                <div>
                  <div className="font-medium text-gray-900">{option.label}</div>
                  <div className="text-sm text-gray-500">{option.desc}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Additional Options */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Audio Processing
          </label>
          <label className="flex items-start space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={removeSilence}
              onChange={(e) => setRemoveSilence(e.target.checked)}
              className="mt-1 text-blue-600 focus:ring-blue-500 rounded"
              disabled={!hasVoice}
            />
            <div>
              <div className="font-medium text-gray-900">Remove Long Silences</div>
              <div className="text-sm text-gray-500">
                Automatically trim extended silent periods
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mobile-stack">
        <button
          onClick={handleGenerateTTS}
          disabled={!text.trim() || !hasVoice || !isSystemReady || isGenerating}
          className="btn-primary flex-1 flex items-center justify-center mobile-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              <span className="hidden sm:inline">Generating Speech...</span>
              <span className="sm:hidden">Generating...</span>
            </>
          ) : (
            <>
              <Send className="w-5 h-5 mr-2" />
              Generate Speech
            </>
          )}
        </button>

        <button
          onClick={clearText}
          disabled={!text.trim()}
          className="btn-secondary flex items-center mobile-full md:w-auto"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Clear
        </button>
      </div>

      {/* Voice Required Notice */}
      {!hasVoice && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800">Voice Required</h4>
            <p className="text-sm text-amber-700 mt-1">
              Please upload and clone a voice sample first before generating speech.
            </p>
          </div>
        </div>
      )}

      {/* Status */}
      {status && (
        <div className={`p-3 rounded-lg flex items-start space-x-2 ${
          status.includes('Error') || status.includes('Failed') 
            ? 'bg-red-50 text-red-700 border border-red-200'
            : status.includes('Generated') || status.includes('successfully')
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-blue-50 text-blue-700 border border-blue-200'
        }`}>
          {status.includes('Error') || status.includes('Failed') ? (
            <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
          ) : (
            <CheckCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
          )}
          <span className="text-sm">{status}</span>
        </div>
      )}

      {/* Generated Audio Playback */}
      {generatedAudioId && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Generated Speech</h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={togglePlayback}
              className="btn-secondary flex items-center"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 mr-2" />
              ) : (
                <Play className="w-4 h-4 mr-2" />
              )}
              {isPlaying ? 'Pause' : 'Play'}
            </button>
            
            <button
              onClick={downloadAudio}
              className="btn-secondary flex items-center"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </button>
          </div>
          
          <audio
            ref={audioRef}
            src={VoiceCloningAPI.getAudioUrl(generatedAudioId)}
            onEnded={handleAudioEnded}
            className="hidden"
          />
        </div>
      )}
    </div>
  );
};

export default TTSSection;

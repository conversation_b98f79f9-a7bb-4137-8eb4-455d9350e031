import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minutes for voice processing
});

export interface VoiceCloneResponse {
  success: boolean;
  message: string;
  voice_info?: string;
  sample_audio_id?: string;
}

export interface TTSResponse {
  success: boolean;
  message: string;
  audio_id?: string;
}

export interface AdvancedSettings {
  temperature?: number;
  repetitionPenalty?: number;
  lengthPenalty?: number;
  topK?: number;
  topP?: number;
  voiceStability?: number;
  emotionStrength?: number;
}

export interface TTSRequest {
  text: string;
  quality_mode: 'high' | 'standard' | 'ultra';
  remove_silence: boolean;
  emotion: 'neutral' | 'excited' | 'calm' | 'questioning';
  speed: number;
  advanced_settings?: AdvancedSettings;
}

export class VoiceCloningAPI {
  static async cloneVoice(
    audioFile: File,
    enhancementLevel: 'none' | 'minimal' | 'gentle' = 'none'
  ): Promise<VoiceCloneResponse> {
    const formData = new FormData();
    formData.append('audio_file', audioFile);
    formData.append('enhancement_level', enhancementLevel);

    const response = await api.post<VoiceCloneResponse>('/clone-voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  static async generateTTS(request: TTSRequest): Promise<TTSResponse> {
    const response = await api.post<TTSResponse>('/generate-tts', request);
    return response.data;
  }

  static getAudioUrl(audioId: string): string {
    return `${API_BASE_URL}/audio/${audioId}`;
  }

  static async deleteAudio(audioId: string): Promise<void> {
    await api.delete(`/audio/${audioId}`);
  }

  static async healthCheck(): Promise<{ status: string; voice_system_ready: boolean }> {
    const response = await api.get('/health');
    return response.data;
  }
}

export default VoiceCloningAPI;

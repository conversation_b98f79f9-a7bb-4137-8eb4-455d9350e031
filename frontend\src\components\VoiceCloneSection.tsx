import React, { useState, useRef } from 'react';
import { Upload, Play, Pause, Download, Loader2, Check<PERSON>ircle, AlertCircle, Mic, Square, RotateCcw } from 'lucide-react';
import VoiceCloningAPI from '../services/api';
import VoiceRecorder from './VoiceRecorder';

interface VoiceCloneSectionProps {
  isSystemReady: boolean;
  onVoiceCloned: (voiceInfo: string, sampleAudioId: string) => void;
  currentVoiceInfo: string | null;
  sampleAudioId: string | null;
}

const VoiceCloneSection: React.FC<VoiceCloneSectionProps> = ({
  isSystemReady,
  onVoiceCloned,
  currentVoiceInfo,
  sampleAudioId,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [enhancementLevel, setEnhancementLevel] = useState<'none' | 'minimal' | 'gentle'>('none');
  const [isCloning, setIsCloning] = useState(false);
  const [status, setStatus] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [inputMethod, setInputMethod] = useState<'upload' | 'record'>('upload');
  const [maxDuration, setMaxDuration] = useState(60.0);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const validateAndSetFile = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('audio/')) {
      setStatus('Please select a valid audio file');
      return false;
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      setStatus('File size must be less than 50MB');
      return false;
    }

    // Rough duration estimation based on file size (approximate)
    const estimatedDurationMinutes = file.size / (1024 * 1024); // Very rough: ~1MB per minute
    const estimatedDurationSeconds = estimatedDurationMinutes * 60;

    if (estimatedDurationSeconds > maxDuration * 3) { // Allow generous buffer for compression differences
      setStatus(`⚠️ File may be too long (estimated ~${estimatedDurationMinutes.toFixed(1)} min). The system will automatically select the best ${maxDuration}s segment.`);
    } else {
      setStatus('');
    }

    setSelectedFile(file);
    return true;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndSetFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetFile(files[0]);
    }
  };

  const handleRecordingComplete = async (audioBlob: Blob, duration: number) => {
    // Convert blob to File object with appropriate extension
    const extension = audioBlob.type.includes('wav') ? '.wav' :
                     audioBlob.type.includes('mp4') ? '.mp4' : '.webm';
    const audioFile = new File([audioBlob], `recording_${Date.now()}${extension}`, {
      type: audioBlob.type,
      lastModified: Date.now(),
    });

    setSelectedFile(audioFile);
    setStatus(`Recording complete (${duration}s). Ready to clone voice.`);
  };

  const handleCloneVoice = async () => {
    if (!selectedFile) {
      setStatus(inputMethod === 'upload' ? 'Please select an audio file first' : 'Please record your voice first');
      return;
    }

    setIsCloning(true);
    setStatus('Processing voice sample...');

    try {
      const response = await VoiceCloningAPI.cloneVoice(selectedFile, enhancementLevel, maxDuration);

      if (response.success && response.sample_audio_id && response.voice_info) {
        setStatus(response.message);
        onVoiceCloned(response.voice_info, response.sample_audio_id);
      } else {
        setStatus(response.message || 'Failed to clone voice');
      }
    } catch (error) {
      console.error('Voice cloning error:', error);
      if (inputMethod === 'record') {
        setStatus('Error: Failed to clone recorded voice. The recording format may not be supported. Try uploading a WAV or MP3 file instead.');
      } else {
        setStatus('Error: Failed to clone voice. Please check if the backend is running and the audio file is valid.');
      }
    } finally {
      setIsCloning(false);
    }
  };

  const togglePlayback = () => {
    if (!audioRef.current || !sampleAudioId) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const downloadSample = () => {
    if (sampleAudioId) {
      const url = VoiceCloningAPI.getAudioUrl(sampleAudioId);
      const a = document.createElement('a');
      a.href = url;
      a.download = `cloned_voice_sample_${sampleAudioId}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  return (
    <div className="space-y-6">
      {/* Input Method Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Choose Input Method
        </label>
        <div className="flex space-x-4">
          <button
            onClick={() => setInputMethod('upload')}
            className={`flex-1 p-3 rounded-lg border-2 transition-all ${
              inputMethod === 'upload'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <Upload className="w-5 h-5 mx-auto mb-1" />
            <div className="text-sm font-medium">Upload File</div>
            <div className="text-xs text-gray-500">WAV, MP3, M4A</div>
          </button>
          <button
            onClick={() => setInputMethod('record')}
            className={`flex-1 p-3 rounded-lg border-2 transition-all ${
              inputMethod === 'record'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <Mic className="w-5 h-5 mx-auto mb-1" />
            <div className="text-sm font-medium">Record Voice</div>
            <div className="text-xs text-gray-500">Direct recording</div>
          </button>
        </div>
      </div>

      {/* File Upload Section */}
      {inputMethod === 'upload' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Reference Voice
          </label>
          <div
            className={`file-upload-zone ${isDragOver ? 'dragover' : ''}`}
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 font-medium">
              {selectedFile && inputMethod === 'upload' ? selectedFile.name : 'Click to upload or drag & drop audio file'}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Supports WAV, MP3, M4A (max 50MB)
            </p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="audio/*"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>
      )}

      {/* Voice Recording Section */}
      {inputMethod === 'record' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Record Your Voice
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <VoiceRecorder
              onRecordingComplete={handleRecordingComplete}
              isDisabled={!isSystemReady}
              maxDuration={maxDuration}
            />
          </div>
        </div>
      )}

      {/* Maximum Duration Control */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Maximum Audio Duration: {maxDuration}s
        </label>
        <div className="space-y-3">
          <input
            type="range"
            min="30"
            max="300"
            step="30"
            value={maxDuration}
            onChange={(e) => setMaxDuration(parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>30s</span>
            <span>120s</span>
            <span>210s</span>
            <span>300s</span>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">Duration Guide:</p>
              <ul className="text-xs space-y-1">
                <li>• <strong>30-60s</strong>: Fast processing, good for testing</li>
                <li>• <strong>60-120s</strong>: Balanced quality and speed</li>
                <li>• <strong>120-300s</strong>: Maximum quality, captures more nuances</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Enhancement Level */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Enhancement Level
        </label>
        <div className="space-y-2">
          {[
            { value: 'none', label: 'None', desc: 'Pure cloning - preserves exact voice characteristics' },
            { value: 'minimal', label: 'Minimal', desc: 'Only removes sub-50Hz frequencies' },
            { value: 'gentle', label: 'Gentle', desc: 'Minimal + very light cleanup' },
          ].map((option) => (
            <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
              <input
                type="radio"
                name="enhancement"
                value={option.value}
                checked={enhancementLevel === option.value}
                onChange={(e) => setEnhancementLevel(e.target.value as any)}
                className="mt-1 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <div className="font-medium text-gray-900">{option.label}</div>
                <div className="text-sm text-gray-500">{option.desc}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Clone Button */}
      <button
        onClick={handleCloneVoice}
        disabled={!selectedFile || !isSystemReady || isCloning}
        className="btn-primary w-full flex items-center justify-center"
      >
        {isCloning ? (
          <>
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
            Cloning Voice...
          </>
        ) : (
          <>
            <CheckCircle className="w-5 h-5 mr-2" />
            Clone Voice
          </>
        )}
      </button>

      {/* Status */}
      {status && (
        <div className={`p-3 rounded-lg flex items-start space-x-2 ${
          status.includes('Error') || status.includes('Failed') 
            ? 'bg-red-50 text-red-700 border border-red-200'
            : status.includes('successfully')
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-blue-50 text-blue-700 border border-blue-200'
        }`}>
          {status.includes('Error') || status.includes('Failed') ? (
            <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
          ) : (
            <CheckCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
          )}
          <span className="text-sm">{status}</span>
        </div>
      )}

      {/* Voice Information */}
      {currentVoiceInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Voice Profile</h3>
          <pre className="text-sm text-gray-600 whitespace-pre-wrap font-mono">
            {currentVoiceInfo}
          </pre>
        </div>
      )}

      {/* Sample Audio Playback */}
      {sampleAudioId && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Cloned Voice Sample</h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={togglePlayback}
              className="btn-secondary flex items-center"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 mr-2" />
              ) : (
                <Play className="w-4 h-4 mr-2" />
              )}
              {isPlaying ? 'Pause' : 'Play'}
            </button>
            
            <button
              onClick={downloadSample}
              className="btn-secondary flex items-center"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </button>
          </div>
          
          <audio
            ref={audioRef}
            src={VoiceCloningAPI.getAudioUrl(sampleAudioId)}
            onEnded={handleAudioEnded}
            className="hidden"
          />
        </div>
      )}
    </div>
  );
};

export default VoiceCloneSection;
